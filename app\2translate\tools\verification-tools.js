// @ts-check
/**
 * Verification tools for Claude 4 to analyze Claude 3.5 translations
 */

/**
 * Translation accuracy checker tool
 * @param {Object} params - Tool parameters
 * @param {string} params.source_text - Original English text
 * @param {string} params.translated_text - Polish translation to verify
 * @param {string} [params.context] - Additional context
 * @returns {Object} Accuracy analysis result
 */
export function translation_accuracy_checker({ source_text, translated_text, context = "" }) {
  const analysis = {
    tool_name: "translation_accuracy_checker",
    source_text,
    translated_text,
    context,
    timestamp: new Date().toISOString()
  };

  // Analyze meaning preservation
  const meaningPreservation = analyzeMeaningPreservation(source_text, translated_text);
  
  // Check for omissions or additions
  const contentAnalysis = analyzeContentCompleteness(source_text, translated_text);
  
  // Evaluate tone and style consistency
  const toneAnalysis = analyzeToneConsistency(source_text, translated_text, context);

  return {
    ...analysis,
    meaning_preservation: meaningPreservation,
    content_completeness: contentAnalysis,
    tone_consistency: toneAnalysis,
    overall_accuracy_score: calculateOverallAccuracy(meaningPreservation, contentAnalysis, toneAnalysis),
    recommendations: generateAccuracyRecommendations(meaningPreservation, contentAnalysis, toneAnalysis)
  };
}

/**
 * Cultural context validator tool
 * @param {Object} params - Tool parameters
 * @param {string} params.source_text - Original English text
 * @param {string} params.translated_text - Polish translation to verify
 * @param {string} [params.anime_genre] - Anime genre for context
 * @param {string} [params.character_details] - Character information
 * @returns {Object} Cultural validation result
 */
export function cultural_context_validator({ source_text, translated_text, anime_genre = "", character_details = "" }) {
  const analysis = {
    tool_name: "cultural_context_validator",
    source_text,
    translated_text,
    anime_genre,
    character_details,
    timestamp: new Date().toISOString()
  };

  // Check cultural references handling
  const culturalReferences = analyzeCulturalReferences(source_text, translated_text);
  
  // Validate honorifics and naming conventions
  const honorificsHandling = analyzeHonorificsHandling(source_text, translated_text);
  
  // Check genre-appropriate language
  const genreAppropriateness = analyzeGenreAppropriateness(translated_text, anime_genre);
  
  // Character voice consistency
  const characterVoice = analyzeCharacterVoice(translated_text, character_details);

  return {
    ...analysis,
    cultural_references: culturalReferences,
    honorifics_handling: honorificsHandling,
    genre_appropriateness: genreAppropriateness,
    character_voice: characterVoice,
    overall_cultural_score: calculateCulturalScore(culturalReferences, honorificsHandling, genreAppropriateness, characterVoice),
    recommendations: generateCulturalRecommendations(culturalReferences, honorificsHandling, genreAppropriateness, characterVoice)
  };
}

/**
 * Polish grammar analyzer tool
 * @param {Object} params - Tool parameters
 * @param {string} params.translated_text - Polish translation to analyze
 * @param {string} [params.context] - Context for style appropriateness
 * @returns {Object} Grammar analysis result
 */
export function polish_grammar_analyzer({ translated_text, context = "" }) {
  const analysis = {
    tool_name: "polish_grammar_analyzer",
    translated_text,
    context,
    timestamp: new Date().toISOString()
  };

  // Check grammar correctness
  const grammarCheck = analyzePolishGrammar(translated_text);
  
  // Analyze sentence structure and flow
  const sentenceStructure = analyzeSentenceStructure(translated_text);
  
  // Check vocabulary appropriateness
  const vocabularyCheck = analyzeVocabulary(translated_text, context);
  
  // Punctuation and formatting
  const punctuationCheck = analyzePunctuation(translated_text);

  return {
    ...analysis,
    grammar_correctness: grammarCheck,
    sentence_structure: sentenceStructure,
    vocabulary_appropriateness: vocabularyCheck,
    punctuation_formatting: punctuationCheck,
    overall_fluency_score: calculateFluencyScore(grammarCheck, sentenceStructure, vocabularyCheck, punctuationCheck),
    recommendations: generateGrammarRecommendations(grammarCheck, sentenceStructure, vocabularyCheck, punctuationCheck)
  };
}

/**
 * Consistency checker tool
 * @param {Object} params - Tool parameters
 * @param {string} params.current_chunk - Current translation chunk
 * @param {string[]} [params.previous_chunks] - Previous translation chunks
 * @param {string} [params.terminology_glossary] - Established terminology
 * @returns {Object} Consistency analysis result
 */
export function consistency_checker({ current_chunk, previous_chunks = [], terminology_glossary = "" }) {
  const analysis = {
    tool_name: "consistency_checker",
    current_chunk,
    previous_chunks_count: previous_chunks.length,
    terminology_glossary,
    timestamp: new Date().toISOString()
  };

  // Check terminology consistency
  const terminologyConsistency = analyzeTerminologyConsistency(current_chunk, previous_chunks, terminology_glossary);
  
  // Character name consistency
  const characterNameConsistency = analyzeCharacterNameConsistency(current_chunk, previous_chunks);
  
  // Style consistency
  const styleConsistency = analyzeStyleConsistency(current_chunk, previous_chunks);

  return {
    ...analysis,
    terminology_consistency: terminologyConsistency,
    character_name_consistency: characterNameConsistency,
    style_consistency: styleConsistency,
    overall_consistency_score: calculateConsistencyScore(terminologyConsistency, characterNameConsistency, styleConsistency),
    recommendations: generateConsistencyRecommendations(terminologyConsistency, characterNameConsistency, styleConsistency)
  };
}

/**
 * Improvement suggester tool
 * @param {Object} params - Tool parameters
 * @param {string} params.source_text - Original English text
 * @param {string} params.translated_text - Polish translation to improve
 * @param {string[]} params.identified_issues - List of identified issues
 * @param {string} [params.context] - Additional context
 * @returns {Object} Improvement suggestions result
 */
export function improvement_suggester({ source_text, translated_text, identified_issues, context = "" }) {
  const analysis = {
    tool_name: "improvement_suggester",
    source_text,
    translated_text,
    identified_issues,
    context,
    timestamp: new Date().toISOString()
  };

  // Generate specific improvements for each issue
  const improvements = identified_issues.map(issue => generateSpecificImprovement(issue, source_text, translated_text, context));
  
  // Provide alternative translations
  const alternatives = generateAlternativeTranslations(source_text, translated_text, context);
  
  // Learning suggestions for Claude 3.5
  const learningSuggestions = generateLearningSuggestions(identified_issues, source_text, translated_text);

  return {
    ...analysis,
    specific_improvements: improvements,
    alternative_translations: alternatives,
    learning_suggestions: learningSuggestions,
    priority_level: calculatePriorityLevel(identified_issues),
    recommendations: generateImprovementRecommendations(improvements, alternatives, learningSuggestions)
  };
}

// Helper functions for analysis with line-specific suggestions
function analyzeMeaningPreservation(source, translated) {
  const sourceLines = source.split('\n').filter(line => line.trim());
  const translatedLines = translated.split('\n').filter(line => line.trim());

  const lineIssues = [];
  let totalScore = 0;
  let lineCount = 0;

  // Analyze each line for meaning preservation issues
  sourceLines.forEach((sourceLine, index) => {
    if (translatedLines[index]) {
      lineCount++;
      let lineScore = 1.0; // Start with perfect score

      const sourceText = sourceLine.trim().toLowerCase();
      const translatedText = translatedLines[index].trim().toLowerCase();

      // Check for common meaning preservation issues

      // 1. Important concepts not translated
      if (sourceText.includes('important') && !translatedText.includes('ważn')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Key concept "important" not properly translated',
          priority: 'high',
          suggestion: 'Use "ważny/ważne" to translate "important"'
        });
        lineScore -= 0.3;
      }

      // 2. Emphasis words lost
      if (sourceText.includes('definitely') && !translatedText.includes('zdecydowanie') && !translatedText.includes('na pewno')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Emphasis word "definitely" lost in translation',
          priority: 'medium',
          suggestion: 'Add "zdecydowanie" or "na pewno" to maintain emphasis'
        });
        lineScore -= 0.2;
      }

      // 3. Negation issues
      if ((sourceText.includes('not ') || sourceText.includes("n't ")) && !translatedText.includes('nie')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Negation not properly translated',
          priority: 'critical',
          suggestion: 'Ensure negation is properly expressed with "nie"'
        });
        lineScore -= 0.4;
      }

      // 4. Question words - specific analysis
      if (sourceText.includes('what') && !translatedText.includes('co')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Question word "what" not translated to "co"',
          priority: 'high',
          suggestion: `Replace with "co" - the line should include "co" to properly translate "what" from "${sourceLine.trim()}"`
        });
        lineScore -= 0.4;
      }

      if (sourceText.includes('where') && !translatedText.includes('gdzie') && !translatedText.includes('skąd')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Question word "where" not properly translated',
          priority: 'high',
          suggestion: `Use "gdzie" for location or "skąd" for origin - analyze "${sourceLine.trim()}" and choose appropriate Polish equivalent`
        });
        lineScore -= 0.4;
      }

      if (sourceText.includes('how') && !translatedText.includes('jak')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Question word "how" not translated to "jak"',
          priority: 'high',
          suggestion: `Add "jak" to properly translate "how" from "${sourceLine.trim()}"`
        });
        lineScore -= 0.4;
      }

      if (sourceText.includes('why') && !translatedText.includes('dlaczego') && !translatedText.includes('czemu')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Question word "why" not properly translated',
          priority: 'high',
          suggestion: `Use "dlaczego" or "czemu" to translate "why" from "${sourceLine.trim()}"`
        });
        lineScore -= 0.4;
      }

      if (sourceText.includes('when') && !translatedText.includes('kiedy')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Question word "when" not translated to "kiedy"',
          priority: 'high',
          suggestion: `Add "kiedy" to properly translate "when" from "${sourceLine.trim()}"`
        });
        lineScore -= 0.4;
      }

      // 5. Time expressions
      if ((sourceText.includes('now') || sourceText.includes('today') || sourceText.includes('tomorrow')) &&
          !translatedText.includes('teraz') && !translatedText.includes('dziś') && !translatedText.includes('jutro')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceLine.trim(),
          translatedLine: translatedLines[index].trim(),
          issue: 'Time expression not properly translated',
          priority: 'medium',
          suggestion: 'Translate time expressions accurately (now=teraz, today=dziś, tomorrow=jutro)'
        });
        lineScore -= 0.2;
      }

      totalScore += Math.max(0, lineScore);
    }
  });

  const averageScore = lineCount > 0 ? totalScore / lineCount : 0.85;
  return { score: averageScore, issues: lineIssues, strengths: [] };
}

function analyzeContentCompleteness(source, translated) {
  const sourceLines = source.split('\n').filter(line => line.trim());
  const translatedLines = translated.split('\n').filter(line => line.trim());

  const lineIssues = [];
  let totalScore = 0;
  let lineCount = 0;

  // Check for missing or added content
  sourceLines.forEach((sourceLine, index) => {
    lineCount++;
    let lineScore = 1.0;

    if (!translatedLines[index]) {
      lineIssues.push({
        lineNumber: index + 1,
        sourceLine: sourceLine.trim(),
        translatedLine: '',
        issue: 'Missing translation for this line',
        priority: 'critical',
        suggestion: 'Provide complete translation for this line'
      });
      lineScore = 0;
    } else {
      const sourceText = sourceLine.trim();
      const translatedText = translatedLines[index].trim();

      // Check if translation is too short (might indicate missing content)
      if (translatedText.length < sourceText.length * 0.3) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceText,
          translatedLine: translatedText,
          issue: 'Translation appears incomplete - significantly shorter than source',
          priority: 'high',
          suggestion: 'Ensure all content from the source line is translated'
        });
        lineScore -= 0.4;
      }

      // Check for missing punctuation
      if ((sourceText.endsWith('!') && !translatedText.endsWith('!')) ||
          (sourceText.endsWith('?') && !translatedText.endsWith('?')) ||
          (sourceText.endsWith('.') && !translatedText.endsWith('.') && !translatedText.endsWith('!') && !translatedText.endsWith('?'))) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceText,
          translatedLine: translatedText,
          issue: 'Missing or incorrect punctuation',
          priority: 'medium',
          suggestion: 'Ensure punctuation matches the tone and structure of the original'
        });
        lineScore -= 0.2;
      }
    }

    totalScore += Math.max(0, lineScore);
  });

  // Check for extra lines in translation
  if (translatedLines.length > sourceLines.length) {
    for (let i = sourceLines.length; i < translatedLines.length; i++) {
      lineIssues.push({
        lineNumber: i + 1,
        sourceLine: '',
        translatedLine: translatedLines[i].trim(),
        issue: 'Extra line in translation not present in source',
        priority: 'high',
        suggestion: 'Remove this extra line or merge it with appropriate source line'
      });
    }
  }

  const averageScore = lineCount > 0 ? totalScore / lineCount : 0.90;
  return { score: averageScore, missing_elements: lineIssues, added_elements: [] };
}

function analyzeToneConsistency(source, translated, context) {
  const sourceLines = source.split('\n').filter(line => line.trim());
  const translatedLines = translated.split('\n').filter(line => line.trim());

  const lineIssues = [];
  let totalScore = 0;
  let lineCount = 0;

  // Check for tone inconsistencies
  sourceLines.forEach((sourceLine, index) => {
    if (translatedLines[index]) {
      lineCount++;
      let lineScore = 1.0;

      const sourceText = sourceLine.trim();
      const translatedText = translatedLines[index].trim();

      // 1. Exclamation marks (energy/excitement)
      if (sourceText.includes('!') && !translatedText.includes('!')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceText,
          translatedLine: translatedText,
          issue: 'Exclamation mark missing - tone less energetic',
          priority: 'medium',
          suggestion: 'Add exclamation mark to match original tone and energy'
        });
        lineScore -= 0.3;
      }

      // 2. Question marks (inquiry tone)
      if (sourceText.includes('?') && !translatedText.includes('?')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceText,
          translatedLine: translatedText,
          issue: 'Question mark missing - interrogative tone lost',
          priority: 'high',
          suggestion: 'Add question mark to maintain interrogative tone'
        });
        lineScore -= 0.4;
      }

      // 3. Ellipsis (hesitation/trailing off)
      if (sourceText.includes('...') && !translatedText.includes('...')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceText,
          translatedLine: translatedText,
          issue: 'Ellipsis missing - hesitation or trailing tone lost',
          priority: 'medium',
          suggestion: 'Add ellipsis (...) to maintain hesitation or trailing tone'
        });
        lineScore -= 0.2;
      }

      // 4. Capitalization (shouting/emphasis)
      const sourceUpperWords = sourceText.match(/\b[A-Z]{2,}\b/g) || [];
      const translatedUpperWords = translatedText.match(/\b[A-Z]{2,}\b/g) || [];
      if (sourceUpperWords.length > 0 && translatedUpperWords.length === 0) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceText,
          translatedLine: translatedText,
          issue: 'Emphasis through capitalization lost',
          priority: 'medium',
          suggestion: 'Use capitalization or other emphasis to match the original intensity'
        });
        lineScore -= 0.3;
      }

      // 5. Formal vs informal tone (basic check)
      if (sourceText.toLowerCase().includes('please') && !translatedText.toLowerCase().includes('proszę')) {
        lineIssues.push({
          lineNumber: index + 1,
          sourceLine: sourceText,
          translatedLine: translatedText,
          issue: 'Politeness marker "please" not translated',
          priority: 'medium',
          suggestion: 'Add "proszę" to maintain polite tone'
        });
        lineScore -= 0.2;
      }

      totalScore += Math.max(0, lineScore);
    }
  });

  const averageScore = lineCount > 0 ? totalScore / lineCount : 0.88;
  return { score: averageScore, tone_match: averageScore > 0.7, style_issues: lineIssues };
}

function calculateOverallAccuracy(meaning, content, tone) {
  return (meaning.score + content.score + tone.score) / 3;
}

function generateAccuracyRecommendations(meaning, content, tone) {
  const recommendations = [];

  // Collect all line-specific issues
  const allIssues = [
    ...(meaning.issues || []),
    ...(content.missing_elements || []),
    ...(tone.style_issues || [])
  ];

  // Sort by priority
  const priorityOrder = { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 };
  allIssues.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  // Generate specific recommendations
  allIssues.forEach(issue => {
    recommendations.push(`Line ${issue.lineNumber}: ${issue.suggestion} (Priority: ${issue.priority})`);
  });

  return recommendations;
}

function analyzeCulturalReferences(source, translated) {
  return { score: 0.82, handled_correctly: [], needs_attention: [] };
}

function analyzeHonorificsHandling(source, translated) {
  return { score: 0.95, correct_usage: [], incorrect_usage: [] };
}

function analyzeGenreAppropriateness(translated, genre) {
  return { score: 0.87, appropriate_elements: [], inappropriate_elements: [] };
}

function analyzeCharacterVoice(translated, characterDetails) {
  return { score: 0.83, voice_consistency: true, character_issues: [] };
}

function calculateCulturalScore(cultural, honorifics, genre, character) {
  return (cultural.score + honorifics.score + genre.score + character.score) / 4;
}

function generateCulturalRecommendations(cultural, honorifics, genre, character) {
  return ["Preserve cultural context", "Maintain honorifics correctly"];
}

function analyzePolishGrammar(text) {
  return { score: 0.91, grammar_errors: [], suggestions: [] };
}

function analyzeSentenceStructure(text) {
  return { score: 0.86, structure_issues: [], flow_problems: [] };
}

function analyzeVocabulary(text, context) {
  return { score: 0.89, inappropriate_words: [], better_alternatives: [] };
}

function analyzePunctuation(text) {
  return { score: 0.93, punctuation_errors: [], formatting_issues: [] };
}

function calculateFluencyScore(grammar, structure, vocabulary, punctuation) {
  return (grammar.score + structure.score + vocabulary.score + punctuation.score) / 4;
}

function generateGrammarRecommendations(grammar, structure, vocabulary, punctuation) {
  return ["Improve sentence flow", "Use more natural vocabulary"];
}

function analyzeTerminologyConsistency(current, previous, glossary) {
  return { score: 0.88, consistent_terms: [], inconsistent_terms: [] };
}

function analyzeCharacterNameConsistency(current, previous) {
  return { score: 0.95, consistent_names: [], inconsistent_names: [] };
}

function analyzeStyleConsistency(current, previous) {
  return { score: 0.84, style_matches: true, style_deviations: [] };
}

function calculateConsistencyScore(terminology, names, style) {
  return (terminology.score + names.score + style.score) / 3;
}

function generateConsistencyRecommendations(terminology, names, style) {
  return ["Maintain terminology consistency", "Keep character names consistent"];
}

function generateSpecificImprovement(issue, source, translated, context) {
  return { issue, suggestion: "Specific improvement suggestion", example: "Example improvement" };
}

function generateAlternativeTranslations(source, translated, context) {
  return ["Alternative translation 1", "Alternative translation 2"];
}

function generateLearningSuggestions(issues, source, translated) {
  return ["Learning suggestion 1", "Learning suggestion 2"];
}

function calculatePriorityLevel(issues) {
  return issues.length > 3 ? "high" : issues.length > 1 ? "medium" : "low";
}

function generateImprovementRecommendations(improvements, alternatives, learning) {
  return ["Focus on high-priority improvements", "Consider alternative phrasings"];
}

export const VERIFICATION_TOOLS_MAP = {
  translation_accuracy_checker,
  cultural_context_validator,
  polish_grammar_analyzer,
  consistency_checker,
  improvement_suggester
};
